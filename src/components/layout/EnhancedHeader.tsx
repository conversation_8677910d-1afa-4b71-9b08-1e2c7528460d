/**
 * Enhanced Header Component
 * 
 * Professional navigation header with dropdown menus,
 * mobile responsiveness, and industry-standard interactions.
 */

import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Menu, X } from 'lucide-react';
import { useLanguage } from '../../context/LanguageContext';
import LanguageSwitcher from '../common/LanguageSwitcher';
import NavigationDropdown from '../navigation/NavigationDropdown';
import MobileNavigationDropdown from '../navigation/MobileNavigationDropdown';

const EnhancedHeader: React.FC = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const location = useLocation();
  const { translations } = useLanguage();

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Close mobile menu when route changes
  useEffect(() => {
    setIsMobileMenuOpen(false);
  }, [location.pathname]);

  // Prevent body scroll when mobile menu is open
  useEffect(() => {
    if (isMobileMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isMobileMenuOpen]);

  const isActive = (path: string) => location.pathname === path;

  const navigationItems = [
    {
      path: '/',
      label: translations.navigation.home,
      exact: true
    },
    {
      path: '/about',
      label: translations.navigation.about
    },
    {
      path: '/guides',
      label: translations.navigation.guides
    },
    {
      path: '/contact',
      label: translations.navigation.contact
    }
  ];

  return (
    <header className={`
      fixed w-full top-0 z-50 transition-all duration-300 ease-in-out
      ${isScrolled
        ? 'bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-100'
        : 'bg-white shadow-sm'
      }
    `}>
      {/* Brand Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link to="/" className="flex items-center space-x-3 group">
              <div className="relative">
                <img
                  src="/assets/images/MaBoursedetudeLogo.jpeg"
                  alt={translations.brand.name}
                  className="h-12 w-auto rounded-lg shadow-md transform transition-transform duration-300 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>
              <div className="flex flex-col">
                <span className="text-2xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent tracking-tight">
                  {translations.brand.name}
                </span>
                <span className="text-xs text-gray-500 font-medium tracking-wider">
                  {translations.brand.tagline}
                </span>
              </div>
            </Link>
          </div>

          {/* Right Side - Language Switcher & Mobile Menu */}
          <div className="flex items-center space-x-4">
            <LanguageSwitcher />

            {/* Mobile menu button */}
            <div className="md:hidden">
              <button
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-primary hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary transition-colors duration-200"
                aria-expanded={isMobileMenuOpen}
                aria-label="Toggle navigation menu"
              >
                {isMobileMenuOpen ? (
                  <X className="h-6 w-6" />
                ) : (
                  <Menu className="h-6 w-6" />
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Professional Navigation Menu - Enhanced Theme */}
      <div className="bg-gradient-to-r from-primary-50 via-white to-primary-50 backdrop-blur-sm border-t border-primary-200/30 shadow-sm">
        <nav className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="hidden md:flex items-center justify-center space-x-6 py-3">
            {/* Home Link */}
            <Link
              to="/"
              className={`
                px-4 py-2 rounded-lg text-sm font-medium
                transition-all duration-200 ease-in-out
                ${isActive('/')
                  ? 'text-primary bg-gradient-to-r from-primary-50 to-primary-100 shadow-md'
                  : 'text-gray-700 hover:text-primary hover:bg-gradient-to-r hover:from-primary-50 hover:to-primary-100 hover:shadow-sm'
                }
              `}
            >
              <span className="transition-colors duration-200">{translations.navigation.home}</span>
            </Link>

            {/* Dropdown Menus - New Order: Scholarships, Opportunities, Countries */}
            <NavigationDropdown
              type="scholarships"
              label={translations.navigation.scholarships}
              className="professional-nav-item"
            />

            <NavigationDropdown
              type="opportunities"
              label={translations.navigation.opportunities}
              className="professional-nav-item"
            />

            <NavigationDropdown
              type="countries"
              label={translations.navigation.countries}
              className="professional-nav-item"
            />

            {/* Regular Navigation Items */}
            {navigationItems.slice(1).map((item) => (
              <Link
                key={item.path}
                to={item.path}
                className={`
                  px-4 py-2 rounded-lg text-sm font-medium
                  transition-all duration-200 ease-in-out
                  ${isActive(item.path)
                    ? 'text-primary bg-gradient-to-r from-primary-50 to-primary-100 shadow-md'
                    : 'text-gray-700 hover:text-primary hover:bg-gradient-to-r hover:from-primary-50 hover:to-primary-100 hover:shadow-sm'
                  }
                `}
              >
                <span className="transition-colors duration-200">{item.label}</span>
              </Link>
            ))}
          </div>
        </nav>
      </div>

      {/* Mobile Navigation Menu */}
      {isMobileMenuOpen && (
        <div className="md:hidden">
          <div className="bg-gradient-to-b from-primary-50 to-white border-t border-primary-200/50 shadow-lg max-h-screen overflow-y-auto">
            {/* Mobile Navigation Items - Same order as desktop */}
            <div className="py-2">
              {/* Home */}
              <Link
                to="/"
                onClick={() => setIsMobileMenuOpen(false)}
                className={`
                  block px-4 py-3 text-base font-medium
                  transition-colors duration-200 ease-in-out
                  ${isActive('/')
                    ? 'text-primary bg-primary/10 border-l-4 border-primary'
                    : 'text-gray-700 hover:text-primary hover:bg-gray-50'
                  }
                `}
              >
                {translations.navigation.home}
              </Link>

              {/* Scholarships with dropdown - New Order */}
              <MobileNavigationDropdown
                type="scholarships"
                label={translations.navigation.scholarships}
                onItemClick={() => setIsMobileMenuOpen(false)}
              />

              {/* Opportunities with dropdown */}
              <MobileNavigationDropdown
                type="opportunities"
                label={translations.navigation.opportunities}
                onItemClick={() => setIsMobileMenuOpen(false)}
              />

              {/* Countries with dropdown */}
              <MobileNavigationDropdown
                type="countries"
                label={translations.navigation.countries}
                onItemClick={() => setIsMobileMenuOpen(false)}
              />

              {/* Guides */}
              <Link
                to="/guides"
                onClick={() => setIsMobileMenuOpen(false)}
                className={`
                  block px-4 py-3 text-base font-medium
                  transition-colors duration-200 ease-in-out
                  ${isActive('/guides')
                    ? 'text-primary bg-primary/10 border-l-4 border-primary'
                    : 'text-gray-700 hover:text-primary hover:bg-gray-50'
                  }
                `}
              >
                {translations.navigation.guides}
              </Link>

              {/* About */}
              <Link
                to="/about"
                onClick={() => setIsMobileMenuOpen(false)}
                className={`
                  block px-4 py-3 text-base font-medium
                  transition-colors duration-200 ease-in-out
                  ${isActive('/about')
                    ? 'text-primary bg-primary/10 border-l-4 border-primary'
                    : 'text-gray-700 hover:text-primary hover:bg-gray-50'
                  }
                `}
              >
                {translations.navigation.about}
              </Link>

              {/* Contact */}
              <Link
                to="/contact"
                onClick={() => setIsMobileMenuOpen(false)}
                className={`
                  block px-4 py-3 text-base font-medium
                  transition-colors duration-200 ease-in-out
                  ${isActive('/contact')
                    ? 'text-primary bg-primary/10 border-l-4 border-primary'
                    : 'text-gray-700 hover:text-primary hover:bg-gray-50'
                  }
                `}
              >
                {translations.navigation.contact}
              </Link>
            </div>
          </div>
        </div>
      )}
    </header>
  );
};

export default EnhancedHeader;
