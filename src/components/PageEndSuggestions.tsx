import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useLanguage } from '../context/LanguageContext';
import { constructImageUrl } from '../utils/imageUtils';
import EnhancedScholarshipCard from './EnhancedScholarshipCard';

interface SuggestionItem {
  id: number;
  title: string;
  thumbnail?: string;
  deadline?: string;
  isOpen?: boolean;
  country?: string;
  level?: string;
  type?: string;
  description?: string;
  slug?: string;
}

interface PageEndSuggestionsProps {
  currentPageType: 'scholarship' | 'country' | 'level' | 'opportunity';
  currentItem?: string;
  excludeId?: number;
  className?: string;
}

const PageEndSuggestions: React.FC<PageEndSuggestionsProps> = ({
  currentPageType,
  currentItem,
  excludeId,
  className = ''
}) => {
  const { translations } = useLanguage();
  const [suggestions, setSuggestions] = useState<SuggestionItem[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchSuggestions();
  }, [currentPageType, currentItem, excludeId]);

  const fetchSuggestions = async () => {
    try {
      setLoading(true);
      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';

      let endpoint = '';
      const params = new URLSearchParams();
      params.append('limit', '6'); // 3x2 layout = 6 items

      if (excludeId) {
        params.append('excludeId', excludeId.toString());
      }

      switch (currentPageType) {
        case 'scholarship':
          endpoint = `${apiUrl}/api/scholarships/latest`;
          break;
        case 'country':
          endpoint = `${apiUrl}/api/scholarships/latest`;
          if (currentItem) {
            params.append('excludeCountry', currentItem);
          }
          break;
        case 'level':
          endpoint = `${apiUrl}/api/scholarships/latest`;
          if (currentItem) {
            params.append('excludeLevel', currentItem);
          }
          break;
        case 'opportunity':
          endpoint = `${apiUrl}/api/opportunities/latest`;
          break;
        default:
          endpoint = `${apiUrl}/api/scholarships/latest`;
      }

      const response = await fetch(`${endpoint}?${params.toString()}`);
      if (response.ok) {
        const data = await response.json();
        setSuggestions(data.data || data.scholarships || []);
      }
    } catch (error) {
      console.error('Error fetching suggestions:', error);
    } finally {
      setLoading(false);
    }
  };

  const getItemLink = (item: SuggestionItem) => {
    switch (currentPageType) {
      case 'opportunity':
        return `/opportunities/${item.id}`;
      default:
        if (item.slug) {
          return `/bourse/${item.slug}`;
        }
        return `/scholarships/${item.id}`;
    }
  };

  const getSectionTitle = () => {
    switch (currentPageType) {
      case 'scholarship':
        return 'Bourses Recommandées';
      case 'country':
        return 'Bourses Recommandées';
      case 'level':
        return 'Bourses Recommandées';
      case 'opportunity':
        return 'Opportunités Recommandées';
      default:
        return 'Recommandations';
    }
  };

  if (loading || suggestions.length === 0) {
    return null;
  }

  return (
    <div className={`bg-gradient-to-br from-gray-50 to-blue-50 py-4 ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-4">
          <h2 className="text-2xl font-bold text-gray-900">
            {getSectionTitle()}
          </h2>
        </div>

        {/* GreatYOP Card Layout */}
        <div className="gy-pcard-wrap">
          {suggestions.slice(0, 6).map((item, index) => (
            <EnhancedScholarshipCard
              key={item.id}
              id={item.id}
              title={item.title}
              thumbnail={item.thumbnail || ''}
              deadline={item.deadline || ''}
              isOpen={item.isOpen !== undefined ? item.isOpen : true}
              level={item.level}
              country={item.country}
              onClick={(id, slug) => {
                const link = getItemLink(item);
                window.location.href = link;
              }}
              index={index}
              variant="greatyop"
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default PageEndSuggestions;
