import React, { useRef, useState } from 'react';
import EnhancedScholarshipCard from './EnhancedScholarshipCard';
import { Scholarship } from './ScholarshipGrid';
import { useLanguage } from '../context/LanguageContext';
import { Clock, Star, BadgeCheck, CurrencyDollar } from './icons/index';

interface EnhancedLatestScholarshipsSectionProps {
  scholarships: Scholarship[];
  loading: boolean;
  onScholarshipClick: (id: number) => void;
}

const EnhancedLatestScholarshipsSection: React.FC<EnhancedLatestScholarshipsSectionProps> = ({
  scholarships,
  loading,
  onScholarshipClick
}) => {
  const sectionRef = useRef<HTMLElement>(null);
  const [activeFilter, setActiveFilter] = useState<string>('latest');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage] = useState<number>(6);
  const { translations } = useLanguage();

  // Professional filter options - Clean Industry Standard
  const filters = [
    { id: 'latest', label: 'Nouvelles Bourses', icon: <Clock className="w-5 h-5" /> },
    { id: 'popular', label: 'Bourses Populaires', icon: <Star className="w-5 h-5" /> },
    { id: 'fully-funded', label: 'Entièrement Financées', icon: <BadgeCheck className="w-5 h-5" /> },
    { id: 'partially-funded', label: 'Partiellement Financées', icon: <CurrencyDollar className="w-5 h-5" /> }
  ];

  // Professional filtering logic - Clean Categories
  const filteredScholarships = scholarships.filter(scholarship => {
    if (activeFilter === 'latest') {
      // Show scholarships with recent deadlines (within next 90 days)
      const deadline = new Date(scholarship.deadline);
      const today = new Date();
      const diffTime = deadline.getTime() - today.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return scholarship.isOpen && diffDays <= 90 && diffDays > 0;
    }

    if (activeFilter === 'popular') {
      // Show open scholarships from popular sources
      return scholarship.isOpen && (
        scholarship.fundingSource === 'Government' ||
        scholarship.fundingSource === 'Gouvernement' ||
        scholarship.fundingSource === 'University' ||
        scholarship.fundingSource === 'Université'
      );
    }

    if (activeFilter === 'fully-funded') {
      // Check if scholarship is likely fully funded based on source and title
      const title = scholarship.title?.toLowerCase() || '';
      return scholarship.isOpen && (
        scholarship.fundingSource === 'Government' ||
        scholarship.fundingSource === 'Gouvernement' ||
        title.includes('fully funded') ||
        title.includes('entièrement financé') ||
        title.includes('full scholarship') ||
        title.includes('bourse complète')
      );
    }

    if (activeFilter === 'partially-funded') {
      // Show scholarships that are not from government (likely partial funding)
      return scholarship.isOpen && (
        scholarship.fundingSource !== 'Government' &&
        scholarship.fundingSource !== 'Gouvernement' &&
        scholarship.fundingSource !== 'Full Scholarship'
      );
    }

    return scholarship.isOpen; // Default fallback
  });

  // Pagination calculations
  const totalPages = Math.ceil(filteredScholarships.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentScholarships = filteredScholarships.slice(startIndex, endIndex);

  // Reset to page 1 when filter changes
  React.useEffect(() => {
    setCurrentPage(1);
  }, [activeFilter]);

  // Professional pagination component
  const ProfessionalPagination = () => {
    if (totalPages <= 1) return null;

    const getPageNumbers = () => {
      const pages = [];
      const maxVisible = 5;

      if (totalPages <= maxVisible) {
        for (let i = 1; i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        if (currentPage <= 3) {
          for (let i = 1; i <= 4; i++) pages.push(i);
          pages.push('...');
          pages.push(totalPages);
        } else if (currentPage >= totalPages - 2) {
          pages.push(1);
          pages.push('...');
          for (let i = totalPages - 3; i <= totalPages; i++) pages.push(i);
        } else {
          pages.push(1);
          pages.push('...');
          for (let i = currentPage - 1; i <= currentPage + 1; i++) pages.push(i);
          pages.push('...');
          pages.push(totalPages);
        }
      }
      return pages;
    };

    return (
      <div className="flex items-center justify-center space-x-2 mt-8">
        {/* Previous button */}
        <button
          onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
          disabled={currentPage === 1}
          className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
            currentPage === 1
              ? 'text-gray-400 cursor-not-allowed'
              : 'text-gray-700 hover:text-primary hover:bg-primary/10'
          }`}
        >
          Précédent
        </button>

        {/* Page numbers */}
        {getPageNumbers().map((page, index) => (
          <button
            key={index}
            onClick={() => typeof page === 'number' && setCurrentPage(page)}
            disabled={page === '...'}
            className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
              page === currentPage
                ? 'bg-primary text-white shadow-sm'
                : page === '...'
                ? 'text-gray-400 cursor-default'
                : 'text-gray-700 hover:text-primary hover:bg-primary/10'
            }`}
          >
            {page}
          </button>
        ))}

        {/* Next button */}
        <button
          onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
          disabled={currentPage === totalPages}
          className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
            currentPage === totalPages
              ? 'text-gray-400 cursor-not-allowed'
              : 'text-gray-700 hover:text-primary hover:bg-primary/10'
          }`}
        >
          Suivant
        </button>
      </div>
    );
  };

  return (
    <section
      ref={sectionRef}
      id="latest-scholarships"
      className="py-3 pb-2 bg-gradient-to-br from-primary-50/30 via-white to-primary-100/20"
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

        {/* Interactive tabs - Professional framed buttons */}
        <div className="flex flex-wrap justify-center gap-3 mb-6">
          {filters.map((filter) => (
            <button
              key={filter.id}
              onClick={() => setActiveFilter(filter.id)}
              className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 border border-gray-300 shadow-sm ${
                activeFilter === filter.id
                  ? 'bg-gradient-to-r from-primary-50 to-primary-100 text-primary border-primary shadow-md transform scale-105'
                  : 'bg-transparent text-gray-700 hover:bg-gradient-to-r hover:from-primary-50 hover:to-primary-100 hover:text-primary hover:border-primary hover:shadow-md hover:transform hover:scale-105'
              }`}
            >
              {filter.icon}
              <span className="ml-2">{filter.label}</span>
            </button>
          ))}
        </div>

        {/* Scholarship grid with loading state - 3x2 grid */}
        {loading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="bg-white rounded-2xl shadow-md overflow-hidden animate-pulse">
                <div className="aspect-[16/9] bg-gray-200"></div>
                <div className="p-5">
                  <div className="h-5 bg-gray-200 rounded w-3/4 mb-3"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2 mb-3"></div>
                  <div className="h-4 bg-gray-200 rounded w-full mb-3"></div>
                  <div className="h-10 bg-gray-100 rounded w-full mt-4"></div>
                </div>
              </div>
            ))}
          </div>
        ) : filteredScholarships.length > 0 ? (
          <>
            <div className="gy-pcard-wrap">
              {currentScholarships.map((scholarship, index) => (
                <EnhancedScholarshipCard
                  key={scholarship.id}
                  id={scholarship.id}
                  title={scholarship.title}
                  thumbnail={scholarship.thumbnail}
                  deadline={scholarship.deadline}
                  isOpen={scholarship.isOpen}
                  level={scholarship.level}
                  fundingSource={scholarship.fundingSource}
                  country={scholarship.country}
                  onClick={onScholarshipClick}
                  featured={false}
                  index={index}
                  variant="greatyop"
                />
              ))}
            </div>

            {/* Professional Pagination */}
            <ProfessionalPagination />
          </>
        ) : (
          <div className="text-center py-12 bg-white rounded-2xl shadow-sm">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 className="mt-2 text-lg font-medium text-gray-900">{translations.scholarships.noResults.title}</h3>
            <p className="mt-1 text-gray-500">{translations.scholarships.noResults.message}</p>
            <div className="mt-6">
              <button
                onClick={() => setActiveFilter('latest')}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary bg-primary/10 hover:bg-primary/20"
              >
                Voir les nouvelles bourses
              </button>
            </div>
          </div>
        )}


      </div>
    </section>
  );
};

export default EnhancedLatestScholarshipsSection;
